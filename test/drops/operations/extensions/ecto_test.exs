defmodule Drops.Operations.Extensions.EctoTest do
  use ExUnit.Case, async: true

  alias Drops.Operations.Extensions.Ecto

  describe "enabled?/1" do
    test "returns true when repo is configured" do
      opts = [repo: Test.Repo]
      assert Ecto.enabled?(opts) == true
    end

    test "returns false when repo is nil" do
      opts = [repo: nil]
      assert Ecto.enabled?(opts) == false
    end

    test "returns false when repo is not configured" do
      opts = [type: :command]
      assert Ecto.enabled?(opts) == false
    end

    test "returns false for empty options" do
      opts = []
      assert Ecto.enabled?(opts) == false
    end
  end

  describe "extend_using_macro/1" do
    test "returns empty quoted code" do
      opts = [repo: Test.Repo]
      result = Ecto.extend_using_macro(opts)
      
      # Should return quoted code (AST)
      assert is_tuple(result)
    end
  end

  describe "extend_operation_runtime/1" do
    test "returns quoted code with Ecto functionality" do
      opts = [repo: Test.Repo]
      result = Ecto.extend_operation_runtime(opts)
      
      # Should return quoted code (AST)
      assert is_tuple(result)
      
      # Convert to string to check contents
      code_string = Macro.to_string(result)
      
      # Should include Ecto.Changeset import
      assert code_string =~ "import Ecto.Changeset"
      
      # Should include callback definitions
      assert code_string =~ "@callback validate"
      assert code_string =~ "@callback cast_changeset"
      
      # Should include delegation functions
      assert code_string =~ "def validate(changeset)"
      assert code_string =~ "def cast_changeset(params, changeset)"
      assert code_string =~ "def changeset(params)"
      assert code_string =~ "def persist(params)"
    end
  end

  describe "extend_operation_definition/1" do
    test "returns quoted code with Ecto functionality" do
      opts = [repo: Test.Repo]
      result = Ecto.extend_operation_definition(opts)
      
      # Should return quoted code (AST)
      assert is_tuple(result)
      
      # Convert to string to check contents
      code_string = Macro.to_string(result)
      
      # Should include Ecto.Changeset import
      assert code_string =~ "import Ecto.Changeset"
      
      # Should include function definitions
      assert code_string =~ "def validate(changeset)"
      assert code_string =~ "def cast_changeset(_params, changeset)"
      assert code_string =~ "def changeset(params)"
    end

    test "includes persist function when repo is configured" do
      opts = [repo: Test.Repo]
      result = Ecto.extend_operation_definition(opts)
      
      code_string = Macro.to_string(result)
      assert code_string =~ "def persist(params)"
    end

    test "does not include persist function when repo is nil" do
      opts = [repo: nil]
      result = Ecto.extend_operation_definition(opts)
      
      code_string = Macro.to_string(result)
      refute code_string =~ "def persist(params)"
    end
  end

  describe "enhanced_validate/3" do
    # These tests would require setting up a proper operation module
    # For now, we'll test the basic structure
    test "function exists and is callable" do
      assert function_exported?(Ecto, :enhanced_validate, 3)
    end
  end

  describe "public API functions" do
    test "validate/2 exists" do
      assert function_exported?(Ecto, :validate, 2)
    end

    test "cast_changeset/3 exists" do
      assert function_exported?(Ecto, :cast_changeset, 3)
    end

    test "changeset/2 exists" do
      assert function_exported?(Ecto, :changeset, 2)
    end

    test "persist/2 exists" do
      assert function_exported?(Ecto, :persist, 2)
    end
  end
end
